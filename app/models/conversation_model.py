"""
MongoDB model definition for conversations in the communication service.

This module defines the Conversation document model using mongoengine ODM.
It includes fields for tracking user-agent conversations with proper validation
and timestamp management.
"""

# Standard library imports
from datetime import datetime

# Third-party imports
from mongoengine import DateTimeField, Document, IntField, <PERSON><PERSON>ield
from mongoengine.errors import ValidationError

# Local imports
from app.grpc_ import communication_pb2


# Conversation model
class Conversation(Document):
    """
    MongoDB document model for conversations between users and agents.

    This model represents a conversation session between a user and an agent,
    storing metadata such as participants, channel information, and timestamps.

    Attributes:
        userId (str): Unique identifier for the user initiating the conversation.
        agentId (str): Unique identifier for the agent involved in the conversation.
        chatType (str): Type of chat for the conversation.
        createdAt (datetime): Timestamp for the creation of the conversation.
        updatedAt (datetime): Timestamp for the last update to the conversation.
        inputTokens (int): Number of input tokens for the conversation.
        outputTokens (int): Number of output tokens for the conversation.
    """

    # MongoDB collection & index configuration
    meta = {
        "collection": "conversations",
        "indexes": [
            # Single field indexes for the frequest queries
            "userId",
            "agentId",
            "chatType",
            "inputTokens",
            "outputTokens",
            # Compound indexes for time-based queries
            ("userId", "createdAt"),
        ],
        "ordering": ["-createdAt"],
    }

    # User indentifier field with required validation
    userId = StringField(required=True)

    # Agent identifier field (optional to match proto)
    agentId = StringField(required=False)

    # Chat type field for the conversation
    chatType = StringField(required=True)

    # Title field for the conversation (set when first message is added)
    title = StringField(required=False)

    # Input tokens field for the conversation
    inputTokens = IntField(required=False, default=0)

    # Output tokens field for the conversation
    outputTokens = IntField(required=False, default=0)

    # Timestamp field for the creation of the conversation
    createdAt = DateTimeField(default=datetime.utcnow)

    # Timestamp field for the last update to the conversation
    updatedAt = DateTimeField(default=datetime.utcnow)

    # Add a validation method to ensure the channel value is valid

    def clean(self):
        """Update the updated timestamp and validate the chat type before saving."""

        # If the creation timestamp is not set
        if not self.createdAt:
            # Set the creation timestamp to the current time
            self.createdAt = datetime.utcnow()

        # Update the updated timestamp
        self.updatedAt = datetime.utcnow()

        # Get chat type enum names
        chat_type_names = [name for name in communication_pb2.ChatType.keys()]

        # If the chat type value is not valid
        if self.chatType not in chat_type_names:
            # Raise a validation error
            raise ValidationError(
                f"Invalid chat type: {self.chatType}. Must be one of: {chat_type_names}"
            )

        # Call the parent clean method
        super().clean()

    # String representation of the conversation document
    def __str__(self):
        """Return the string representation of the conversation document."""

        # Return the string representation of the conversation document
        return f"Conversation({self.id}, {self.userId}, {self.agentId}, {self.chatType})"

    # Convert the conversation document to a dictionary
    def to_dict(self):
        """Convert the conversation document to a dictionary."""

        # Return the conversation document as a dictionary
        return {
            "id": str(self.id),
            "userId": self.userId,
            "agentId": self.agentId,
            "chatType": self.chatType,
            "title": self.title,
            "inputTokens": self.inputTokens,
            "outputTokens": self.outputTokens,
            "createdAt": self.createdAt,
            "updatedAt": self.updatedAt,
        }

    # Convert the conversation document to a gRPC message
    def to_proto(self):
        """Convert the conversation document to a gRPC message."""

        # Create a new conversation message
        conversation = communication_pb2.Conversation()

        # Set the conversation ID
        conversation.id = str(self.id)

        # Set the user ID
        conversation.userId = self.userId

        # Set the agent ID if it exists
        if self.agentId:
            conversation.agentId = self.agentId

        # Convert string chat type to enum for protobuf
        if self.chatType:
            conversation.chatType = communication_pb2.ChatType.Value(self.chatType)

        # Set the title if it exists
        if self.title:
            conversation.title = self.title

        # Set the input tokens
        conversation.inputTokens = self.inputTokens

        # Set the output tokens
        conversation.outputTokens = self.outputTokens

        # Set the creation timestamp
        conversation.createdAt.FromDatetime(self.createdAt)

        # Set the update timestamp
        conversation.updatedAt.FromDatetime(self.updatedAt)

        # Return the conversation message
        return conversation
