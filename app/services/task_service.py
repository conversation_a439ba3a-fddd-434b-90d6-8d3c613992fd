"""
Service implementation for handling task-related operations.
"""

# Standard libraries
import grpc
from google.protobuf import empty_pb2

# Local imports
from app.grpc_ import communication_pb2, communication_pb2_grpc
from app.models.conversation_model import Conversation
from app.models.task_model import Task
from app.utils.logger import setup_logger

# Initialize logger
logger = setup_logger("communication-service/services/task_service.py")


# Task service class
class TaskService(communication_pb2_grpc.CommunicationServiceServicer):
    """
    Service class for handling task-related operations.
    Implements CRUD operations for tasks.
    """

    # Create task
    def createTask(
        self,
        request: communication_pb2.CreateTaskRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Task:
        """
        Create a new task.

        Args:
            request: CreateTaskRequest containing task details and user ID
            context: gRPC servicer context

        Returns:
            Created task if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Creating new task",
                title=request.title,
                globalChatConversationId=request.globalChatConversationId,
                agentConversationId=request.agentConversationId,
                agentId=request.agentId,
                correlationId=request.correlationId,
                taskStatus=request.taskStatus,
                sessionId=request.sessionId,
            )

            # Check if the user is authorized to create a task in this conversation
            try:
                conversation = Conversation.objects.get(id=request.globalChatConversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized task creation attempt",
                        globalChatConversationId=request.globalChatConversationId,
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to create tasks in conversation {request.globalChatConversationId}",
                    )
            except Conversation.DoesNotExist:
                logger.error(
                    "Conversation not found", conversationId=request.globalChatConversationId
                )
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {request.globalChatConversationId} not found",
                )

            # Convert int enum to string enum name
            task_status_name = communication_pb2.TaskStatus.Name(request.taskStatus)

            # Create task in database
            task = Task(
                title=request.title,
                globalChatConversationId=request.globalChatConversationId,
                agentConversationId=request.agentConversationId,
                agentId=request.agentId,
                correlationId=request.correlationId if request.correlationId else None,
                taskStatus=task_status_name,
                sessionId=request.sessionId if request.sessionId else None,
            )
            task.save()

            # Log success
            logger.info("Task created successfully", taskId=task.id)

            # Convert to protobuf message
            return task.to_proto()

        except Exception as e:
            # Log error
            logger.error("Failed to create task", error=str(e))

            # Raise gRPC exception
            context.abort(
                grpc.StatusCode.INTERNAL, f"Failed to create task: {str(e)}"
            )

    # Delete task
    def deleteTask(
        self,
        request: communication_pb2.DeleteTaskRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Delete a task.

        Args:
            request: DeleteTaskRequest containing task ID and user ID
            context: gRPC servicer context

        Returns:
            Empty response if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Deleting task", taskId=request.taskId, userId=request.userId
            )

            # Get the task
            try:
                task = Task.objects.get(id=request.taskId)
            except Task.DoesNotExist:
                logger.error("Task not found", taskId=request.taskId)
                context.abort(
                    grpc.StatusCode.NOT_FOUND, f"Task {request.taskId} not found"
                )

            # Check if the user is authorized to delete this task
            try:
                conversation = Conversation.objects.get(id=task.globalChatConversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized task deletion attempt",
                        taskId=request.taskId,
                        globalChatConversationId=str(task.globalChatConversationId),
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to delete task {request.taskId}",
                    )
            except Conversation.DoesNotExist:
                logger.error(
                    "Conversation not found", conversationId=str(task.globalChatConversationId)
                )
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {task.globalChatConversationId} not found",
                )

            # Delete task from database
            task.delete()

            # Log success
            logger.info("Task deleted successfully", taskId=request.taskId)

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to delete task", error=str(e))

            # Raise gRPC exception
            context.abort(
                grpc.StatusCode.INTERNAL, f"Failed to delete task: {str(e)}"
            )

    # Update task status
    def updateTaskStatus(
        self,
        request: communication_pb2.UpdateTaskStatusRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Update the status of a task.

        Args:
            request: UpdateTaskStatusRequest containing task ID, new status, and user ID
            context: gRPC servicer context

        Returns:
            Empty response if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Updating task status",
                taskId=request.taskId,
                taskStatus=request.taskStatus,
                userId=request.userId,
            )

            # Get the task
            try:
                task = Task.objects.get(id=request.taskId)
            except Task.DoesNotExist:
                logger.error("Task not found", taskId=request.taskId)
                context.abort(
                    grpc.StatusCode.NOT_FOUND, f"Task {request.taskId} not found"
                )

            # Check if the user is authorized to update this task
            try:
                conversation = Conversation.objects.get(id=task.globalChatConversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized task status update attempt",
                        taskId=request.taskId,
                        globalChatConversationId=str(task.globalChatConversationId),
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to update task {request.taskId}",
                    )
            except Conversation.DoesNotExist:
                logger.error(
                    "Conversation not found", conversationId=str(task.globalChatConversationId)
                )
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {task.globalChatConversationId} not found",
                )

            # Convert int enum to string enum name
            task_status_name = communication_pb2.TaskStatus.Name(request.taskStatus)

            # Update task status
            task.taskStatus = task_status_name
            task.save()

            # Log success
            logger.info("Task status updated successfully", taskId=request.taskId, newStatus=task_status_name)

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to update task status", error=str(e))

            # Raise gRPC exception
            context.abort(
                grpc.StatusCode.INTERNAL, f"Failed to update task status: {str(e)}"
            )

    # List tasks
    def listTasks(
        self,
        request: communication_pb2.ListTasksRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.ListTasksResponse:
        """
        List tasks for a conversation.

        Args:
            request: ListTasksRequest containing conversation ID and user ID
            context: gRPC servicer context

        Returns:
            List of tasks if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Listing tasks",
                globalChatConversationId=request.globalChatConversationId,
                userId=request.userId,
            )

            # Check if the user is authorized to list tasks in this conversation
            try:
                conversation = Conversation.objects.get(id=request.globalChatConversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized task listing attempt",
                        globalChatConversationId=request.globalChatConversationId,
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to list tasks in conversation {request.globalChatConversationId}",
                    )
            except Conversation.DoesNotExist:
                logger.error(
                    "Conversation not found", conversationId=request.globalChatConversationId
                )
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {request.globalChatConversationId} not found",
                )

            # Pagination parameters with robust type checking
            try:
                page_size = int(request.limit)
                if page_size <= 0:
                    page_size = 10
            except (TypeError, ValueError):
                page_size = 10

            try:
                page = int(request.page)
                if page <= 0:
                    page = 1
            except (TypeError, ValueError):
                page = 1

            skip = (page - 1) * page_size

            # Build task filter query for the specified conversation
            task_filter = Task.objects.filter(
                globalChatConversationId=request.globalChatConversationId
            )

            # Get paginated results
            tasks = task_filter[skip : skip + page_size]
            total_count = task_filter.count()
            total_pages = (total_count + page_size - 1) // page_size

            # Log success
            logger.info(
                "Tasks listed successfully", globalChatConversationId=request.globalChatConversationId
            )

            # Build pagination metadata
            metadata = communication_pb2.PaginationMetadata(
                total=total_count,
                totalPages=total_pages,
                currentPage=page,
                pageSize=page_size,
                hasNextPage=page < total_pages,
                hasPreviousPage=page > 1,
            )

            # Convert to protobuf messages with debug logging
            proto_tasks = []
            for idx, task in enumerate(tasks):
                try:
                    proto = task.to_proto()
                    if not hasattr(proto, "DESCRIPTOR"):
                        logger.error(
                            "to_proto() did not return a protobuf message",
                            index=idx,
                            type=str(type(proto)),
                            value=str(proto),
                        )
                    proto_tasks.append(proto)
                except Exception as e:
                    logger.error(
                        "Exception in task.to_proto()",
                        index=idx,
                        error=str(e),
                        task_repr=repr(task),
                    )
            logger.info(
                "Returning ListTasksResponse",
                proto_types=[str(type(t)) for t in proto_tasks],
                count=len(proto_tasks),
            )
            return communication_pb2.ListTasksResponse(
                data=proto_tasks,
                metadata=metadata,
            )

        except Exception as e:
            # Log error
            logger.error("Failed to list tasks", error=str(e))

            # Raise gRPC exception
            context.abort(
                grpc.StatusCode.INTERNAL, f"Failed to list tasks: {str(e)}"
            )
