"""
Service implementation for handling conversation-related operations.
"""

# Third-party imports
import grpc
from dotenv import load_dotenv
from google.protobuf import empty_pb2

# Local imports
from app.grpc_ import communication_pb2, communication_pb2_grpc
from app.models.conversation_model import Conversation
from app.models.message_model import Message
from app.utils.logger import setup_logger

# Load environment variables from .env file
load_dotenv()

# Initialize logger
logger = setup_logger("communication-service/services/conversation_service.py")


# Conversation service class
class ConversationService(communication_pb2_grpc.CommunicationServiceServicer):
    """
    Service class for handling conversation-related operations.
    Implements CRUD operations for conversations.
    """

    # Create conversation
    def createConversation(
        self,
        request: communication_pb2.CreateConversationRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Conversation:
        """
        Create a new conversation or return existing one if it has no messages.

        Args:
            request: CreateConversationRequest containing conversation details
            context: gRPC servicer context

        Returns:
            Created conversation or existing conversation with no messages
        """
        try:
            # Log request details
            logger.info(
                "Creating new conversation or checking existing ones",
                userId=request.userId,
                agentId=request.agentId if request.agentId else None,
                chatType=request.chatType,
            )

            # Convert int chatType to string enum name for filtering
            chat_type_name = communication_pb2.ChatType.Name(request.chatType)

            # Build filter parameters for existing conversations
            filter_params = {
                "userId": request.userId,
                "chatType": chat_type_name,
            }
            
            # Add agentId to filter if provided
            if request.agentId:
                filter_params["agentId"] = request.agentId

            # Check for existing conversations with the same criteria - get most recent directly
            most_recent_conversation = Conversation.objects.filter(**filter_params).order_by('-createdAt').first()

            if most_recent_conversation:
                logger.info(
                    "Found existing conversation, checking for messages",
                    conversationId=str(most_recent_conversation.id),
                )

                # Check if the most recent conversation has any messages
                message_count = Message.objects.filter(conversationId=most_recent_conversation.id).count()
                
                if message_count == 0:
                    # No messages in the most recent conversation, return it
                    logger.info(
                        "Most recent conversation has no messages, returning existing conversation",
                        conversationId=str(most_recent_conversation.id),
                    )
                    return most_recent_conversation.to_proto()
                else:
                    logger.info(
                        "Most recent conversation has messages, creating new conversation",
                        conversationId=str(most_recent_conversation.id),
                        messageCount=message_count,
                    )

            # Either no existing conversations or most recent has messages, create new one
            conversation = Conversation(
                userId=request.userId,
                agentId=request.agentId if request.agentId else None,
                chatType=chat_type_name,
            )
            conversation.save()

            # Log success
            logger.info(
                "Conversation created successfully", conversationId=conversation.id
            )

            # Convert to protobuf message
            return conversation.to_proto()

        except Exception as e:
            # Log error
            logger.error("Failed to create conversation", error=str(e))

            # Raise gRPC error
            context.abort(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    # Get conversation
    def getConversation(
        self,
        request: communication_pb2.GetConversationRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Conversation:
        """
        Get conversation by ID.

        Args:
            request: GetConversationRequest containing conversation ID and user ID
            context: gRPC servicer context

        Returns:
            Conversation with the specified ID if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Getting conversation",
                conversationId=request.conversationId,
                userId=request.userId,
            )

            # Get conversation from database
            conversation = Conversation.objects.get(id=request.conversationId)

            # Check if the user is authorized to access this conversation
            if conversation.userId != request.userId:
                logger.warning(
                    "Unauthorized access attempt",
                    conversationId=request.conversationId,
                    requestUserId=request.userId,
                    conversationUserId=conversation.userId,
                )
                context.abort(
                    grpc.StatusCode.PERMISSION_DENIED,
                    f"User {request.userId} is not authorized to access conversation {request.conversationId}",
                )

            # Log success
            logger.info(
                "Conversation retrieved successfully", conversationId=conversation.id
            )

            # Convert to protobuf message
            return conversation.to_proto()

        except Exception as e:
            # Log error
            logger.error("Failed to get conversation", error=str(e))

            # Raise gRPC error
            context.abort(
                grpc.StatusCode.INTERNAL, f"Failed to get conversation: {str(e)}"
            )

    # Update conversation tokens
    def updateConversationTokens(
        self,
        request: communication_pb2.UpdateConversationTokensRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Update conversation tokens.

        Args:
            request: UpdateConversationTokensRequest containing conversation ID and user ID
            context: gRPC servicer context

        Returns:
            Empty response
        """
        try:
            # Log request details
            logger.info(
                "Updating conversation tokens",
                conversationId=request.conversationId,
                inputTokens=request.inputTokens,
                outputTokens=request.outputTokens,
                userId=request.userId,
            )

            # Get conversation from database
            conversation = Conversation.objects.get(id=request.conversationId)

            # Check if the user is authorized to update tokens for this conversation
            if conversation.userId != request.userId:
                logger.warning(
                    "Unauthorized token update attempt",
                    conversationId=request.conversationId,
                    requestUserId=request.userId,
                    conversationUserId=conversation.userId,
                )
                context.abort(
                    grpc.StatusCode.PERMISSION_DENIED,
                    f"User {request.userId} is not authorized to update tokens for conversation {request.conversationId}",
                )

            # Update conversation tokens
            conversation.inputTokens = request.inputTokens if request.inputTokens else conversation.inputTokens
            conversation.outputTokens = request.outputTokens if request.outputTokens else conversation.outputTokens
            conversation.save()

            # Log success
            logger.info(
                "Conversation tokens updated successfully",
                conversationId=request.conversationId,
                inputTokens=conversation.inputTokens,
                outputTokens=conversation.outputTokens,
            )

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to update conversation tokens", error=str(e))

            # Raise gRPC error
            context.abort(
                grpc.StatusCode.INTERNAL,
                f"Failed to update conversation tokens: {str(e)}",
            )

    # Delete conversation
    def deleteConversation(
        self,
        request: communication_pb2.DeleteConversationRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Delete conversation by ID.

        Args:
            request: DeleteConversationRequest containing conversation ID and user ID
            context: gRPC servicer context

        Returns:
            Empty response if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Deleting conversation",
                conversationId=request.conversationId,
                userId=request.userId,
            )

            # Get conversation from database
            conversation = Conversation.objects.get(id=request.conversationId)

            # Check if the user is authorized to delete this conversation
            if conversation.userId != request.userId:
                logger.warning(
                    "Unauthorized deletion attempt",
                    conversationId=request.conversationId,
                    requestUserId=request.userId,
                    conversationUserId=conversation.userId,
                )
                context.abort(
                    grpc.StatusCode.PERMISSION_DENIED,
                    f"User {request.userId} is not authorized to delete conversation {request.conversationId}",
                )

            # Delete conversation from database
            conversation.delete()

            # Log success
            logger.info(
                "Conversation deleted successfully",
                conversationId=request.conversationId,
            )

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to delete conversation", error=str(e))

            # Raise gRPC error
            context.abort(
                grpc.StatusCode.INTERNAL, f"Failed to delete conversation: {str(e)}"
            )

    # List conversations
    def listConversations(
        self,
        request: communication_pb2.ListConversationsRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.ListConversationsResponse:
        """
        List conversations.

        Args:
            request: ListConversationsRequest containing query parameters
            context: gRPC servicer context

        Returns:
            List of conversations
        """
        try:
            # Log request details
            logger.info(
                "Listing conversations",
                userId=request.userId,
                chatType=request.chatType,
                agentId=request.agentId if request.agentId else None,
            )

            # Pagination parameters with robust type checking
            try:
                page_size = int(request.limit)
                if page_size <= 0:
                    page_size = 10
            except (TypeError, ValueError):
                page_size = 10

            try:
                page = int(request.page)
                if page <= 0:
                    page = 1
            except (TypeError, ValueError):
                page = 1

            skip = (page - 1) * page_size

            # Start building the filter with userId (mandatory)
            filter_params = {"userId": request.userId}

            # Add agentId to filter params if provided
            if request.agentId:
                filter_params["agentId"] = request.agentId

            # Add chatType filter if specified (not UNSPECIFIED)
            if request.chatType != 0:
                chat_type_name = communication_pb2.ChatType.Name(request.chatType)
                filter_params["chatType"] = chat_type_name

            # Apply the filters
            conversation_filter = Conversation.objects.filter(**filter_params)

            # Get paginated results
            conversations = conversation_filter[skip : skip + page_size]
            total_count = conversation_filter.count()
            total_pages = (total_count + page_size - 1) // page_size

            # Log success
            logger.info(
                "Conversations listed successfully",
                userId=request.userId,
                chatType=request.chatType,
                agentId=request.agentId if request.agentId else None,
                count=total_count,
            )

            # Build pagination metadata
            metadata = communication_pb2.PaginationMetadata(
                total=total_count,
                totalPages=total_pages,
                currentPage=page,
                pageSize=page_size,
                hasNextPage=page < total_pages,
                hasPreviousPage=page > 1,
            )

            # Convert to protobuf messages
            return communication_pb2.ListConversationsResponse(
                data=[conversation.to_proto() for conversation in conversations],
                metadata=metadata,
            )

        except Exception as e:
            # Log error
            logger.error("Failed to list conversations", error=str(e))

            # Raise gRPC error
            context.abort(
                grpc.StatusCode.INTERNAL, f"Failed to list conversations: {str(e)}"
            )
