"""
Service implementation for handling message-related operations.
"""

# Standard libraries
import grpc
from google.protobuf import any_pb2, empty_pb2

# Local imports
from app.grpc_ import communication_pb2, communication_pb2_grpc
from app.models.conversation_model import Conversation
from app.models.message_model import Message
from app.utils.logger import setup_logger

# Initialize logger
logger = setup_logger("communication-service/services/message_service.py")


# Message service class
class MessageService(communication_pb2_grpc.CommunicationServiceServicer):
    """
    Service class for handling message-related operations.
    Implements CRUD operations for messages with JSON data support.
    """

    # Create message
    def createMessage(
        self,
        request: communication_pb2.CreateMessageRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Message:
        """
        Create a new message with JSON data support.

        Args:
            request: CreateMessageRequest containing message details and user ID
            context: gRPC servicer context

        Returns:
            Created message if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Creating new message",
                conversationId=request.conversationId,
                senderType=request.senderType,
                hasData=bool(request.HasField("data")),
                workflowId=request.workflowId,
                workflowResponse=request.workflowResponse,
            )

            # Check if the user is authorized to create a message in this conversation
            try:
                conversation = Conversation.objects.get(id=request.conversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized message creation attempt",
                        conversationId=request.conversationId,
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to create messages in conversation {request.conversationId}",
                    )
            except Conversation.DoesNotExist:
                logger.error("Conversation not found", conversationId=request.conversationId)
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {request.conversationId} not found",
                )

            # Convert int enums to string enum names
            sender_type_name = communication_pb2.SenderType.Name(request.senderType)

            # Convert protobuf Struct data to dictionary for storage
            message_data = None
            if request.HasField("data"):
                try:
                    # Use MessageToDict for proper conversion of all nested structures
                    from google.protobuf.json_format import MessageToDict

                    message_data = MessageToDict(request.data)
                except Exception as e:
                    logger.error("Failed to convert protobuf Struct to dictionary", error=str(e))
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT, f"Invalid data format: {str(e)}"
                    )

            # Populate the workflowResponse map field
            workflow_response_map = {}
            if request.workflowResponse:
                for key, value in request.workflowResponse.items():
                    any_value = any_pb2.Any()
                    any_value.Pack(value)
                    workflow_response_map[key] = any_value

            # Create message in database
            message = Message(
                conversationId=request.conversationId,
                senderType=sender_type_name,
                data=message_data,
                workflowId=request.workflowId,
                workflowResponse=workflow_response_map,
            )
            message.save()

            # Update conversation title if not already set
            try:
                if not conversation.title and message_data:
                    # Extract title from message data
                    title = self._extract_title_from_message_data(message_data)
                    if title:
                        conversation.title = title
                        conversation.save()
                        logger.info(
                            "Updated conversation title",
                            conversationId=str(conversation.id),
                            title=title,
                        )
            except Exception as e:
                logger.warning("Failed to update conversation title", error=str(e))

            # Log success
            logger.info("Message created successfully", messageId=message.id)

            # Convert to protobuf message
            return message.to_proto()

        except Exception as e:
            # Log error
            logger.error("Failed to create message", error=str(e))

            # Raise gRPC exception
            context.abort(grpc.StatusCode.INTERNAL, f"Failed to create message: {str(e)}")

    # Delete message
    def deleteMessage(
        self,
        request: communication_pb2.DeleteMessageRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Delete a message.

        Args:
            request: DeleteMessageRequest containing message ID and user ID
            context: gRPC servicer context

        Returns:
            Empty response if the user is authorized
        """
        try:
            # Log request details
            logger.info("Deleting message", messageId=request.messageId, userId=request.userId)

            # Get the message
            try:
                message = Message.objects.get(id=request.messageId)
            except Message.DoesNotExist:
                logger.error("Message not found", messageId=request.messageId)
                context.abort(grpc.StatusCode.NOT_FOUND, f"Message {request.messageId} not found")

            # Check if the user is authorized to delete this message
            try:
                conversation = Conversation.objects.get(id=message.conversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized message deletion attempt",
                        messageId=request.messageId,
                        conversationId=str(message.conversationId),
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to delete message {request.messageId}",
                    )
            except Conversation.DoesNotExist:
                logger.error("Conversation not found", conversationId=str(message.conversationId))
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {message.conversationId} not found",
                )

            # Delete message from database
            message.delete()

            # Log success
            logger.info("Message deleted successfully", messageId=request.messageId)

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to delete message", error=str(e))

            # Raise gRPC exception
            context.abort(grpc.StatusCode.INTERNAL, f"Failed to delete message: {str(e)}")

    # List messages
    def listMessages(
        self,
        request: communication_pb2.ListMessagesRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.ListMessagesResponse:
        """
        List messages for a conversation with JSON data support.

        Args:
            request: ListMessagesRequest containing conversation ID and user ID
            context: gRPC servicer context

        Returns:
            List of messages with JSON data if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Listing messages",
                conversationId=request.conversationId,
                userId=request.userId,
            )

            # Check if the user is authorized to list messages in this conversation
            try:
                conversation = Conversation.objects.get(id=request.conversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized message listing attempt",
                        conversationId=request.conversationId,
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to list messages in conversation {request.conversationId}",
                    )
            except Conversation.DoesNotExist:
                logger.error("Conversation not found", conversationId=request.conversationId)
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {request.conversationId} not found",
                )

            # Pagination parameters with robust type checking
            try:
                page_size = int(request.limit)
                if page_size <= 0:
                    page_size = 10
            except (TypeError, ValueError):
                page_size = 10

            try:
                page = int(request.page)
                if page <= 0:
                    page = 1
            except (TypeError, ValueError):
                page = 1

            skip = (page - 1) * page_size

            # Build message filter query for the specified conversation
            message_filter = Message.objects.filter(conversationId=request.conversationId)

            # Get paginated results
            messages = message_filter[skip : skip + page_size]
            total_count = message_filter.count()
            total_pages = (total_count + page_size - 1) // page_size

            # Log success
            logger.info("Messages listed successfully", conversationId=request.conversationId)

            # Build pagination metadata
            metadata = communication_pb2.PaginationMetadata(
                total=total_count,
                totalPages=total_pages,
                currentPage=page,
                pageSize=page_size,
                hasNextPage=page < total_pages,
                hasPreviousPage=page > 1,
            )

            # Convert to protobuf messages with debug logging for JSON data
            proto_messages = []
            for idx, message in enumerate(messages):
                try:
                    proto = message.to_proto()
                    if not hasattr(proto, "DESCRIPTOR"):
                        logger.error(
                            "to_proto() did not return a protobuf message",
                            index=idx,
                            type=str(type(proto)),
                            value=str(proto),
                        )
                    proto_messages.append(proto)
                except Exception as e:
                    logger.error(
                        "Exception in message.to_proto()",
                        index=idx,
                        error=str(e),
                        message_repr=repr(message),
                    )
            logger.info(
                "Returning ListMessagesResponse with JSON data support",
                proto_types=[str(type(m)) for m in proto_messages],
                count=len(proto_messages),
            )
            return communication_pb2.ListMessagesResponse(
                data=proto_messages,
                metadata=metadata,
            )

        except Exception as e:
            # Log error
            logger.error("Failed to list messages", error=str(e))

            # Raise gRPC exception
            context.abort(grpc.StatusCode.INTERNAL, f"Failed to list messages: {str(e)}")

    def _extract_title_from_message_data(self, message_data):
        """
        Extract a title from message data.

        Args:
            message_data: Dictionary containing message data

        Returns:
            str: Extracted title or None
        """
        if not message_data:
            return None

        data = message_data.get("data", {})

        if not data:
            return None

        title = data.get("message")

        return title[:97]
